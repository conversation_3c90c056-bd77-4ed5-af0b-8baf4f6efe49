<template>
    <van-nav-bar title="产品详细" left-text="返回" left-arrow @click-left="onClickLeft" />
    <rt-swipe :id="id"/>
    <rt-info :id="id"/>
    <rt-tabs :id="id"/>
    <rt-action :id="id"/>
</template>
<script>
import RtSwipe from './components/RtSwipe.vue';
import RtInfo from './components/RtInfo.vue';
import RtTabs from './components/RtTabs.vue';
import RtAction from './components/RtAction.vue';
export default {
    data() {
        return {
            id: parseInt(this.$route.query.id),
        }
    },
    components: {
        RtSwipe,
        RtInfo,
        RtTabs,
        RtAction
    },
    methods: {
        onClickLeft() {
            history.back();
        }
    },
}
</script>
<style scoped></style>