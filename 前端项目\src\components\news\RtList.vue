<template>
    <van-nav-bar title="动态" />
    <van-grid :column-num="1" class="news-grid" :center="false">
        <van-grid-item v-for="item, key in news" @click="onNavTo(item.id)" :key="key" class="news-item">
            <h3>{{ item.title }}</h3>
            <van-image :src="item.src" />
            <p>{{ item.desc }}</p>
            <div class="date">发布日期：{{ item.date }}</div>
        </van-grid-item>
    </van-grid>
</template>
<script>
import data from "@/data/shop"
export default {
    data() {
        return {
            news: data.news
        }
    },
    methods: {
        onNavTo(id) {
            this.$router.push({
                path: '/disp',
                query: { id: id }
            })
        }
    }
}
</script>
<style scoped>
.news-grid {
    margin-bottom: 50px;
}

.news-item h3 {
    font-size: 16px;
    padding-bottom: 5px;
    margin: -8px 0 5px 0;

}

.news-item p {
    font-size: 12px;
    color: #696969;
    margin: 5px 0;
    line-height: 18px;
}

.news-item .date {
    font-size: 13px;
    color: #696969;
    margin-top: 5px;
}
</style>