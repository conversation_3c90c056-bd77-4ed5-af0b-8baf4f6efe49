<template>
    <van-tabbar v-model="active" v-show="blnShow">
        <van-tabbar-item replace to="/" icon="home-o">首页</van-tabbar-item>
        <van-tabbar-item replace to="/cate" icon="apps-o">分类</van-tabbar-item>
        <van-tabbar-item replace to="/news" icon="guide-o">动态</van-tabbar-item>
        <van-tabbar-item replace to="/my" icon="user-o">我的</van-tabbar-item>
    </van-tabbar>
</template>
<script>
export default {
    data() {
        return {
            active: 0
        }
    },
    props: ["blnShow"],
}
</script>