<template>
    <van-grid :column-num="3" :border="false">
        <van-grid-item v-for="item, key in items" class="prod-item" :key="key">
            <van-image :src="item.src" />
            <p>{{ item.name }}</p>
        </van-grid-item>
    </van-grid>
</template>
<script>
export default {
    props: ["items"]
}
</script>
<style scoped>
.prod-item p {
    font-size: 13px;
    color: #666;
    margin-top: 5px;
}
</style>