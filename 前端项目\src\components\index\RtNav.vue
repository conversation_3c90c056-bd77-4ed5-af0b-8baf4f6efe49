<template>
    <van-row class="nav-row">
        <van-col :span="6" class="nav-col" v-for="item, key in imgs" :key="key">
            <a :href="item.url"><van-image class="img" :src="item.src" fit="contain" /></a>
        </van-col>
    </van-row>
</template>
<script>
import nav_dz from "@/assets/images/navs/nav_dz.jpg";
import nav_pb from "@/assets/images/navs/nav_pb.jpg";
import nav_pj from "@/assets/images/navs/nav_pj.jpg";
import nav_xg from "@/assets/images/navs/nav_xg.jpg";
export default {
    data() {
        return {
            imgs: [
                { src: nav_dz, url: "http://www.baidu.com" },
                { src: nav_pb, url: "http://www.baidu.com" },
                { src: nav_pj, url: "http://www.baidu.com" },
                { src: nav_xg, url: "http://www.baidu.com" }
            ]
        }
    }
}
</script>
<style scoped>
.nav-row {
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.nav-col {
    text-align: center;
}

.img {
    width: 70px;
}
</style>