{"name": "vue3shop", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@vant/area-data": "^1.5.0", "core-js": "^3.8.3", "pinia": "^2.1.6", "pinia-plugin-persist": "^1.0.0", "vant": "^4.6.0", "vue": "^3.2.13", "vue-router": "^4.2.4"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {"no-unused-vars": "off"}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}