<template>
    <van-nav-bar title="新闻详细" left-text="返回" left-arrow @click-left="onClickLeft" />
    <div class="disp" v-html="curNews[0].detail"></div>
    <rt-action :id="id" />
</template>
<script>
import data from "@/data/shop"
import RtAction from './components/RtAction.vue';
export default {
    data() {
        return {
            id:this.$route.query.id,
            curNews: data.news.filter(item => item.id == this.$route.query.id)
        }
    },
    components: {
        RtAction
    },
    methods: {
        onClickLeft() {
            history.back();
        }
    }
}
</script>
<style scoped>
.disp {
    padding: 10px 15px;
    line-height: 23px;
    margin-bottom: 75px;
}
</style>