<template>
  <router-view></router-view>
  <rt-tab-bar :blnShow="router.currentRoute.meta.showTabBar"/>
</template>

<script>
import RtTabBar from "./components/bottom/RtTabBar.vue";
import { useRouter } from "vue-router";
export default {
  name: 'App',
  data(){
    return{
      router: useRouter()
    }
  },
  components: {
    RtTabBar
  }
}
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

h3,
p {
  padding: 0;
  margin: 0;
}
</style>
