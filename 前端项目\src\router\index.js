import { createRouter, createWebHistory, createWebHashHistory } from 'vue-router';

// 配置组件对应路径
const routes = [
    {
        path: '/',
        name: 'home',
        meta: {
            showTabBar: true
        },
        component: () => import('../components/RtHome.vue')
    },
    {
        path: '/cate',
        name: 'cate',
        meta: {
            showTabBar: true
        },
        component: () => import('../components/category/RtIndex.vue')
    },
    {
        path: '/news',
        name: 'news',
        meta: {
            showTabBar: true
        },
        component: () => import('../components/news/RtList.vue')
    },
    {
        path: '/disp',
        name: 'disp',
        meta: {
            showTabBar: false
        },
        component: () => import('../components/news/RtDisplay.vue')
    },
    {
        path: '/cart',
        name: 'cart',
        meta: {
            showTabBar: false
        },
        component: () => import('../components/cart/RtIndex.vue')
    },
    {
        path: '/pay',
        name: 'pay',
        meta: {
            showTabBar: false
        },
        component: () => import('../components/pay/RtIndex.vue')
    },
    {
        path: '/paysuccess',
        name: 'paysuccess',
        meta: {
            showTabBar: false
        },
        component: () => import('../components/pay/components/RtPaySuccess.vue')
    },
    {
        path: '/product',
        name: 'product',
        meta: {
            showTabBar: false
        },
        component: () => import('../components/product/RtIndex.vue')
    },
    {
        path: '/order/:active',
        name: 'order',
        meta: {
            showTabBar: false
        },
        component: () => import('../components/my/components/RtOrderList.vue')
    },
    {
        path: '/collect',
        name: 'collect',
        meta: {
            showTabBar: false
        },
        component: () => import('../components/my/components/RtCollectList.vue')
    },
    {
        path: '/addresslist',
        name: 'addresslist',
        meta: {
            showTabBar: false
        },
        component: () => import('../components/my/address/RtList.vue')
    },
    {
        path: '/addressedit',
        name: 'addressedit',
        meta: {
            showTabBar: false
        },
        component: () => import('../components/my/address/RtEdit.vue')
    },
    {
        path: '/my',
        name: 'my',
        meta: {
            showTabBar: true
        },
        component: () => import('../components/my/RtIndex.vue')
    },
    {
        path: '/:pathMatch(.*)*',
        name: 'ErrPage',
        component: () => import('../error/ErrPage.vue'),
    },
    {
        path:'/login',
        name:'login',
        meta: {
            showTabBar: false,
            title:'登录'
        },
        component: () => import('../components/user/UserLogin.vue')
    },
    {
        path:'/register',
        name:'register',
        meta: {
            showTabBar: false,
            title:'注册'
        },
        component: () => import('../components/user/UserRegister.vue')
    }
]
const router = createRouter({
    // history: createWebHistory(process.env.BASE_URL),
    history: createWebHashHistory(process.env.BASE_URL),
    routes
})

export default router