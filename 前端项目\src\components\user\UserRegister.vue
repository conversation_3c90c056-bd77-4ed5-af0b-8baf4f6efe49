<template>
  <div class="register-container">
    <div class="header">
      <h1>创建账号</h1>
      <p>加入我们，开启精彩体验</p>
    </div>
    
    <van-form @submit="handleRegister">
      <!-- 用户名 -->
      <van-field
        v-model="form.username"
        name="username"
        label="用户名"
        placeholder="4-20字符(字母数字下划线)"
        :rules="[{ required: true, message: '请填写用户名' }]"
      >
        <template #left-icon>
          <van-icon name="user-o" />
        </template>
      </van-field>
      
      <!-- 密码 -->
      <van-field
        v-model="form.password"
        type="password"
        name="password"
        label="密码"
        placeholder="6-20位字母数字组合"
        :rules="[{ required: true, message: '请设置密码' }]"
      >
        <template #left-icon>
          <van-icon name="lock" />
        </template>
      </van-field>
      
      <!-- 确认密码 -->
      <van-field
        v-model="form.confirmPassword"
        type="password"
        name="confirmPassword"
        label="确认密码"
        placeholder="请再次输入密码"
        :rules="[
          { required: true, message: '请确认密码' },
          { validator: validatePassword, message: '两次输入密码不一致' }
        ]"
      >
        <template #left-icon>
          <van-icon name="lock" />
        </template>
      </van-field>
      
      <!-- 手机号 -->
      <van-field
        v-model="form.phone"
        name="phone"
        label="手机号"
        placeholder="请输入11位手机号"
        :rules="[{ required: true, message: '请填写手机号' }]"
      >
        <template #left-icon>
          <van-icon name="phone-o" />
        </template>
      </van-field>
      
      <!-- 邮箱 -->
      <van-field
        v-model="form.email"
        name="email"
        label="邮箱"
        placeholder="请输入常用邮箱"
        :rules="[{ required: true, message: '请填写邮箱' }]"
      >
        <template #left-icon>
          <van-icon name="envelop-o" />
        </template>
      </van-field>
      
      <div class="agreement">
        <van-checkbox v-model="agreed">
          我已阅读并同意
          <router-link to="/">《用户协议》</router-link>
          和
          <router-link to="/">《隐私政策》</router-link>
        </van-checkbox>
      </div>
      
      <div style="margin: 30px 16px;">
        <van-button 
          round 
          block 
          type="primary" 
          native-type="submit"
          :loading="loading"
        >
          注册
        </van-button>
      </div>
    </van-form>
    
    <div class="footer-links">
      <router-link to="/login">已有账号？立即登录</router-link>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import axios from 'axios'

const router = useRouter()

// 表单数据
const form = reactive({
  username: '',
  password: '',
  confirmPassword: '',
  phone: '',
  email: ''
})

const agreed = ref(false)
const loading = ref(false)

// 密码验证
const validatePassword = () => {
  return form.password === form.confirmPassword
}

// 注册提交
const handleRegister = async () => {
  if (!agreed.value) {
    showToast('请阅读并同意用户协议和隐私政策')
    return
  }
  
  if (form.password !== form.confirmPassword) {
    showToast('两次输入密码不一致')
    return
  }
  
  try {
    loading.value = true
    
    // 调用注册API
    const response = await axios.post('/user/register', {
      username: form.username,
      password: form.password,
      phone: form.phone,
      email: form.email
    })
    
     if (response.data.code === 200) {
        alert('注册成功，请登录')
        router.push('/login')
      } else {
        alert(`注册失败：${response.data.msg}`)
      }
    
  } catch (error) {
    console.error("注册请求错误详情:", error); // 打印完整错误对象
    // 处理错误响应
    const errorMessage = error.response?.data?.msg || error.msg || '注册失败';
    showToast({ message: errorMessage, icon: 'fail' });
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
/* 保持原有样式不变 */
.register-container {
  padding: 20px;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.header {
  text-align: center;
  margin-bottom: 40px;
  padding-top: 30px;
}

.header h1 {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.header p {
  font-size: 14px;
  color: #666;
}

.agreement {
  margin: 20px 0;
  font-size: 12px;
  color: #666;
}

.agreement a {
  color: #1989fa;
}

.footer-links {
  text-align: center;
  margin-top: 20px;
  font-size: 14px;
}

.footer-links a {
  color: #1989fa;
  text-decoration: none;
}
</style>