<template>
    <div class="p-info">
        <h3>{{ info.title }}</h3>
        <p class="p-title"><van-tag plain type="danger">{{ info.bright }} </van-tag>
            {{ info.desc }}</p>
        <p class="p-price">￥{{ info.price }}</p>
    </div>
    <div class="p-tip">
        <van-cell icon="passed" is-link title="支持花呗分期" @click="show = true" />
        <van-action-sheet v-model:show="show" title="服务说明">
            <div class="p-content">
                <h3><van-icon name="passed" />支持花呗分期</h3>
                <p>本商品支持花呗分期，无手续费用</p>
                <h3><van-icon name="passed" />支持以旧换新</h3>
                <p>本商品以旧换新，最高可抵500元</p>
            </div>
        </van-action-sheet>
    </div>
</template>
<script>
import data from "@/data/shop"
import { getProdById } from "@/utils/prod";
export default {
    props: ["id"],
    data() {
        return {
            info: getProdById(data.prods, this.id),
            show: false
        }
    }
}
</script>
<style scoped>
.p-info,
.p-tip {
    padding: 10px 15px;
    color: #515151;
    border-bottom: solid 1px #cccc;
}

.van-cell,
.p-tip {
    background-color: #eee;
}

.p-info .p-title {
    margin: 5px 0;
    font-size: 15px;
    line-height: 25px;
}

.p-info .p-price {
    font-size: 26px;
    color: red;
    margin-left: -6px;
}

.p-content {
    color: #515151;
    padding: 10px 30px;
    line-height: 32px;
    margin-bottom: 30px;
}

.p-content p {
    margin-left: 18px;
    margin-bottom: 10px;
}
</style>