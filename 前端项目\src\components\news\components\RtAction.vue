<template>
    <div class="action">
        <van-button @click="add_like_act(id)" :icon="retNewsLikes.length > 0 ? 'good-job' : 'good-job-o'" plain round
            size="small" hairline type="primary">
            {{ retNewsLikes.length }}
        </van-button>
        <van-button @click="add_collect_act({ 'id': id, 'type': 2 })" :icon="retBlnCollected ? 'like' : 'like-o'" plain round
            size="small" hairline type="primary">
            {{ retBlnCollected ? '已收藏' : '收藏' }}
        </van-button>
    </div>
</template>
<script>
import { mapState, mapActions } from 'pinia'
import { shopStore } from "@/store/shopStore"
export default {
    props: ["id"],
    methods: {
        ...mapActions(shopStore, ["add_collect_act", "add_like_act"])
    },
    computed: {
        ...mapState(shopStore, ["collects", "likes"]),
        retBlnCollected() {
            return this.collects.filter(item => item.id == this.id && item.type == 2).length > 0 ? true : false;
        },
        retNewsLikes() {
            return this.likes.filter(item => item == this.id);
        }
    }
}
</script>
<style scoped>
.action {
    padding: 15px 0;
    display: flex;
    justify-content: space-around;
    position: fixed;
    width: 100%;
    left: 0;
    bottom: 0;
    background-color: #fff;
}

.van-button {
    width: 90px;
}
</style>