<template>
    <van-row>
        <van-col :span="24" class="header">
            <van-image class="logo" :src="logo" fit="contain" />
        </van-col>
    </van-row>
    <van-row>
        <van-col :span="24" class="header-nav">
            <van-image class="nav" :src="nav" fit="contain" />
        </van-col>
    </van-row>
</template>
<script>
import logo from "@/assets/images/logo.jpg"
import nav from "@/assets/images/nav.png"
export default {
    data() {
        return {
            logo: logo,
            nav: nav
        }
    }
}
</script>
<style scoped>
.header {
    position: fixed;
    top: 0;
    background: white;
    z-index: 1;
    width: 100%;
    border-bottom: 1px solid #eee;
}

.header-nav {
    margin-top: 50px;
}

.logo {
    width: 100px;
    height: 50px;
    margin-left: 10px;
}

.nav {
    width: 100%;
    height: 50px;
}</style>