<template>
  <van-nav-bar title="分类" />
  <rt-search  @onInputSearch="onInputSearch" />
  <div class="cate">
    <div class="cate-left">
      <rt-cate-left :items="leftItems" @onChange="onChange" />
    </div>
    <div class="cate-right">
      <rt-cate-right :items="curItems" />
    </div>
  </div>
</template>
   
<script> 
import data from "@/data/shop"
import RtSearch from "./components/RtSearch.vue";
import RtCateLeft from "./components/RtCateLeft.vue";
import RtCateRight from "./components/RtCateRight.vue";
export default {
  name: 'RtIndex',
  data() {
    return {
      leftItems: data.leftItems,
      rightItems: data.rightItems,
      curItems: [],
      searchkey:""
    }
  },
  components: {
    RtSearch,
    RtCateLeft,
    RtCateRight
  },
  methods: {
    onInputSearch(key){
         this.curItems = this.rightItems.filter(item=>item.name.includes(key))
    },
    onChange(idx) {
      this.curItems = [];
      if (idx) {
        this.curItems = this.rightItems.filter(item => item.cid == idx);
      } else {
        this.curItems = this.rightItems;
      }
    }
  },
  mounted() {
    this.curItems = this.rightItems;
  }
}
</script>
   
<style scoped>
.cate {
  display: flex;
  justify-content: space-between;
}

.cate-left {
  width: 120px;
}

.cate-right {
  width: 100%;
}
</style>
   