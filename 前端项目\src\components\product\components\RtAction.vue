<template>
    <van-action-bar>
        <van-action-bar-icon color="blue" @click="add_collect_act({ 'id': id, 'type': 1 })" :icon="retBlnCollected ? 'like' : 'like-o'"
            :text="retBlnCollected ? '已收藏' : '收藏'" />
        <van-action-bar-icon icon="cart-o" text="购物车" :badge="carts.length" @click="enter" />
        <van-action-bar-button type="warning" text="加入购物车" @click="addCarts" />
        <van-action-bar-button type="danger" text="立即购买" @click="goAddCarts"/>
    </van-action-bar>
</template>
<script>
import { mapState, mapActions } from 'pinia'
import { shopStore } from "@/store/shopStore"
import data from "@/data/shop"
import { getProdById } from "@/utils/prod";
export default {
    props: ["id"],
    methods: {
        ...mapActions(shopStore, ["add_collect_act", "add_carts_act"]),
        enter() {
            this.$router.push('/cart')
        },
        addCarts() {
            let prod = getProdById(data.prods, this.id);
            let _prod = {
                id: prod.id,
                title: prod.title,
                num: 1,
                price: prod.price,
                img:prod.swiper[0].src
            }
            this.add_carts_act(_prod);
        },
        goAddCarts(){
            this.addCarts();
            this.enter();
        }
    },
    computed: {
        ...mapState(shopStore, ["collects", "carts"]),
        retBlnCollected() {
            return this.collects.filter(item => item.id == this.id && item.type == 1).length > 0 ? true : false;
        }
    }
}
</script>
<style scoped></style>