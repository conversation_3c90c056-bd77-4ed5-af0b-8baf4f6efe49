import { defineStore } from "pinia";
export const shopStore = defineStore("shop_id", {
    state: () => {
        return {
            collects: [],
            likes: [],
            userInfo: {},
            addressInfo: [],
            carts:[],
            orders:[]
        }
    },
    actions: {
        add_collect_act(data) {
            let collect = this.collects.filter(item => item == data);
            if (collect.length == 0) {
                this.collects.push(data);
            }
        },
        del_collect_act(id) {
            let index = this.collects.findIndex(item => item.id == id);
            this.collects.splice(index,1);
        },
        add_like_act(data) {
            let like = this.likes.filter(item => item == data);
            if (like.length == 0) {
                this.likes.push(data);
            }
        },
        edit_userInfo_act(data) {
            this.userInfo = data;
        },
        add_addressInfo_act(data) {
            let id = this.addressInfo.length + 1;
            data.id = id;
            data.address = data.country + data.province + data.city + data.county + data.addressDetail;
            this.addressInfo.push(data);
        },
        edit_addressInfo_act(index, data) {
            this.addressInfo[index] = data;
        },
        delete_addressInfo_act(index) {
            this.addressInfo.splice(index,1);
        },
        add_carts_act(data) {
            let cart = this.carts.filter(item => item.id == data.id);
            if (cart.length > 0) {
                cart[0].num++;
            } else {
                this.carts.push(data);
            }
        },
        delete_carts_act(id) {
            let _index = this.carts.findIndex(item=>item.id==id);
            this.carts.splice(_index,1);
        },
        edit_carts_num_act(data, type) {
            let cart = this.carts.filter(item => item.id == data.id);
            if (type == 'add') {
                cart[0].num++;
            } else {
                if (cart[0].num > 1) {
                    cart[0].num--;
                }
            }
        },
        add_orders_act(data){
            let id = 'rt'+this.orders.length + 1;
            data.id = id;
            this.orders.push(data);
        },
        delete_orders_act(id) {
            let _index = this.orders.findIndex(item=>item.id==id);
            this.orders.splice(_index,1);
        },
    },
    persist: {
        enabled: true,
        strategies: [
            {
                storage: localStorage,
                paths: ['collects', 'likes', 'userInfo', 'addressInfo','carts','orders']
            }
        ]
    }
})