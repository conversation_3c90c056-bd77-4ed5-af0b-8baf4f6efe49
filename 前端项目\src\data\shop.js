import swipe0 from "@/assets/images/swipe/swipe0.jpg";
import swipe1 from "@/assets/images/swipe/swipe1.jpg";
import swipe2 from "@/assets/images/swipe/swipe2.jpg";
import swipe3 from "@/assets/images/swipe/swipe3.jpg";
import pswipe1 from "@/assets/images/product/swipe/01.png";
import pswipe2 from "@/assets/images/product/swipe/02.png";
import pswipe3 from "@/assets/images/product/swipe/03.png";
import pswipe4 from "@/assets/images/product/swipe/04.png";
import pswipe5 from "@/assets/images/product/swipe/05.png";
import pswipe6 from "@/assets/images/product/swipe/06.png";
import pimg1 from "@/assets/images/product/detail/01.png";
import pimg2 from "@/assets/images/product/detail/02.png";
import pimg3 from "@/assets/images/product/detail/03.png";
import pimg4 from "@/assets/images/product/detail/04.png";
import pimg5 from "@/assets/images/product/detail/05.png";
import pimg6 from "@/assets/images/product/detail/06.png";
import pimg7 from "@/assets/images/product/detail/07.png";
import p01 from "@/assets/images/prod/p01.jpg";
import p02 from "@/assets/images/prod/p02.jpg";
import p03 from "@/assets/images/prod/p03.jpg";
import p04 from "@/assets/images/prod/p04.jpg";
import c01 from "@/assets/images/cate/c01.png";
import c02 from "@/assets/images/cate/c02.png";
import c03 from "@/assets/images/cate/c03.png";
import c04 from "@/assets/images/cate/c04.png";
import c05 from "@/assets/images/cate/c05.png";
import c06 from "@/assets/images/cate/c06.png";
import c07 from "@/assets/images/cate/c07.png";
import c08 from "@/assets/images/cate/c08.png";
import c09 from "@/assets/images/cate/c09.png";
import c10 from "@/assets/images/cate/c10.png";
import c11 from "@/assets/images/cate/c11.png";
import c12 from "@/assets/images/cate/c12.png";
let data = {
    leftItems:[
        {
          "id": "0",
          "name": "全部分类"
        },
        {
          "id": "1",
          "name": "Xplay"
        },
        {
          "id": "2",
          "name": "X系列"
        },
        {
          "id": "3",
          "name": "Y系列"
        },
        {
          "id": "4",
          "name": "充电器"
        },
        {
          "id": "5",
          "name": "保护膜"
        },
        {
          "id": "6",
          "name": "耳机音响"
        },
        {
          "id": "7",
          "name": "智能外设"
        }
      ],
    rightItems:[
        {
          id: 1,
          cid: 2,
          name: "X9Plus",
          src: c01
        },
        {
          id: 2,
          cid: 2,
          name: "X20Plus",
          src: c02
        },
        {
          id: 3,
          cid: 3,
          name: "Y69",
          src: c03
        },
        {
          id: 4,
          cid: 1,
          name: "Xplay6",
          src: c04
        },
        {
          id: 5,
          cid: 3,
          name: "Y55",
          src: c05
        },
        {
          id: 6,
          cid: 3,
          name: "Y66",
          src: c06
        },
        {
          id: 7,
          cid: 4,
          name: "闪充充电器",
          src: c07
        },
        {
          id: 8,
          cid: 6,
          name: "原装音乐耳机",
          src: c08
        },
        {
          id: 9,
          cid: 4,
          name: "原装数据线",
          src: c09
        },
        {
          id: 10,
          cid: 5,
          name: "原装钢化膜",
          src: c10
        },
        {
          id: 11,
          cid: 5,
          name: "高清保护膜",
          src: c11
        },
        {
          id: 12,
          cid: 7,
          name: "乐心手环",
          src: c12
        },
      ],
    prods:[
        {
            id: 1001,
            title: "X18 青春版",
            price: "1100.00",
            desc: '潮流镜面渐变色',
            bright: "花呗免息，0首付0利率轻松购机",
            src: p01,
            imgs:[pimg1, pimg2, pimg3, pimg4, pimg5],
            detail:'<h3>包装清单</h3><span>标配</span><p>X18 青春版 A *1 </p><p>取卡针 *1 </p><p>XE680线控耳机 *1</p><p>闪充充电头 *1</p><p>USB数据线</p><p>透明后盖保护壳 *1</p><p>快速入门指南 *1</p><p>重要信息和保修卡</p><h3>其他参数</h3><div><p>CPU</p><p>高通骁龙八核MSM8976SG（MSM8976pro</p></div>',
            swiper: [
              { src: pswipe1, url: "http://www.baidu.com" },
              { src: pswipe2, url: "http://www.baidu.com" },
              { src: pswipe3, url: "http://www.baidu.com" }
          ]
        },
        {
            id: 1002,
            title: "X20Plus 全面屏",
            price: "1500.00",
            desc: 'X20A 18:9高清全面屏',
            bright: "感恩促销直降200，到手价2598",
            src: p02,
            imgs:[pimg6, pimg7],
            detail:'<h3>包装清单</h3><span>标配</span><p>X9s Plus A *1 </p><p>取卡针 *1 </p><p>XE680线控耳机 *1</p><p>闪充充电头 *1</p><p>USB数据线</p><p>透明后盖保护壳 *1</p><p>快速入门指南 *1</p><p>重要信息和保修卡</p><h3>其他参数</h3><div><p>CPU</p><p>高通骁龙八核MSM8976SG（MSM8976pro</p></div>',
            swiper: [
              { src: pswipe4, url: "http://www.baidu.com" },
              { src: pswipe5, url: "http://www.baidu.com" },
              { src: pswipe6, url: "http://www.baidu.com" }
          ]
        },
        {
            id: 1003,
            title: "Y69 全面屏手机",
            price: "2500.00",
            desc: '前置2400万像素',
            src: p03
        },
        {
            id: 1004,
            title: "Xplay6 128G版",
            price: "1800.00",
            desc: '后置双摄,镭射镌刻',
            src: p04
        }
    ],
    news: [{
        id: 1,
        title: "构建模板内容",
        desc: "构建模板的内容是使用模板功能的前提，一般通过下列几种方式来实现。直接在页面中添加元素和Angular指令，实现应用需求。",
        src: swipe0,
        date: "2023-01-02",
        detail: '<p><span style="margin-left: 30px">从开始的概述中我们知道，Angular是基于HTML基础进行扩展的开发工具，其扩展的目的就是希望能通过HTML标签构建动态的Web应用。要实现这样的目的，需要在Angular内部利用了两项技术点，一个是数据的双向绑定，另一个是依赖注入</span>。<br/><span style="margin-left: 30px">下面我们来简单介绍这两个新概念。</span><br><img style="width:100%;margin:10px 0" src="http://rttop.cn/shop/images/icon.jpg" alt="" /><span style="margin-left: 30px">在Angular中，数据绑定可以通过{<!-- -->{}}双花括号的方式向页面的DOM元素中插入数据，也可以通过添加元素属性的方式绑定Angular的内部指令，实现对元素的数据绑定，这两种形式的数据绑定都是双向同步的，即只要一端发生了变化，绑定的另一端会自动进行同步。</span><br><span style="margin-left: 30px">依赖注入是Angular中一个特有的代码编写方式，其核心思想是在编写代码时，只需要关注为实现页面功能要调用的对象是什么，而不必了解它需依赖于什么，像逻辑类中的$scope对象就是通过依赖注入的方式进行使用的。</span><br><span style="margin-left: 30px">这两项技术点，我们将在后续的章节中进行详细介绍，在此只作概念了解即可。</span><br><span style="margin-left: 30px">在Angular框架中，通过双向绑定和依赖注入这两个功能，极大减少了用户的代码开发量，只需要像声明一个HTML元素一样，就可以轻松构建一个复杂的Web端应用，而这种方式构建的应用的全部代码都由客户端的JavaScript代码完成。因此，Angular框架也是有效解决端（客户端）对端（服务端）应用的方案之一。</span></p>'
    }, {
        id: 2,
        title: "使用指令复制元素",
        desc: "在构建模板内容的过程中，有时需要反复将不同的数据加载到一个元素中，例如，通过元素绑定一个数组的各成员。",
        src: swipe1,
        date: "2023-02-06"
    }, {
        id: 3,
        title: "添加元素样式",
        desc: "直接绑定值为CSS类别名称的$scope对象属性这种方式的操作非常简单，先在控制器中添加一个值为CSS类别名称的属性。",
        src: swipe2,
        date: "2023-03-08"
    }, {
        id: 4,
        title: "控制元素的隐藏与显示状态",
        desc: "可以通过“ng-show”“ng-hide”和“ng-switch”指令来控制元素隐藏与显示的状态，前两个指令直接控制元素的显示和隐藏状态。",
        src: swipe3,
        date: "2023-05-02"
    }]
}
export default data