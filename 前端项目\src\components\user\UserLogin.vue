<template>
  <div class="login-container">
    <div class="header">
      <h1>欢迎回来</h1>
      <p>请登录您的账号</p>
    </div>
    
    <van-form @submit="handleLogin">
      <!-- 用户名 -->
      <van-field
        v-model="form.username"
        name="username"
        label="用户名"
        placeholder="请输入用户名"
        :rules="[{ required: true, message: '请填写用户名' }]"
      >
        <template #left-icon>
          <van-icon name="user-o" />
        </template>
      </van-field>
      
      <!-- 密码 -->
      <van-field
        v-model="form.password"
        type="password"
        name="password"
        label="密码"
        placeholder="请输入密码"
        :rules="[{ required: true, message: '请填写密码' }]"
      >
        <template #left-icon>
          <van-icon name="lock" />
        </template>
      </van-field>
      
      <div class="remember">
        <van-checkbox v-model="rememberMe">记住我</van-checkbox>
        <router-link to="/forgot-password">忘记密码?</router-link>
      </div>
      
      <div style="margin: 30px 16px;">
        <van-button 
          round 
          block 
          type="primary" 
          native-type="submit"
          :loading="loading"
        >
          登录
        </van-button>
      </div>
    </van-form>
    
    <div class="footer-links">
      <router-link to="/register">没有账号？<span class="register-link">立即注册</span></router-link>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import axios from 'axios'
import { useUserStore } from '@/store/user'

const router = useRouter()
const userStore = useUserStore()

// 表单数据
const form = reactive({
  username: '',
  password: ''
})

const rememberMe = ref(false)
const loading = ref(false)

// 登录提交
const handleLogin = async () => {
  try {
    loading.value = true
    // 调用登录API
    const response = await axios.post('/user/login', {
      username: form.username,
      password: form.password
    })
    // 处理响应
    if (response.data && response.data.code === 200) {
      const token = response.data.data
      // 1. 存储token到localStorage
      localStorage.setItem('token', token)
      // 2. 存储到状态管理
      userStore.setUser({
        username: form.username,
        token: token
      })
      // 3. 提示成功
      showToast({
        message: '登录成功',
        icon: 'success',
        duration: 1500
      })
      // 4. 重定向到首页或来源页面
      setTimeout(() => {
        // 检查是否有重定向路径
        const redirect = router.currentRoute.value.query.redirect || '/'
        router.push(redirect)
      }, 1500)
    } else {
      // 处理业务错误
      const errorMsg = response.data?.msg || '登录失败，请重试'
      showToast({
        message: errorMsg,
        icon: 'fail'
      })
    }
  } catch (error) {
    // 错误处理
    let errorMessage = '登录失败，请重试'
    
    if (error.response) {
      // 从响应中获取错误信息
      errorMessage = error.response.data?.msg || 
                    error.response.data?.message || 
                    `请求错误: ${error.response.status}`
    } else if (error.request) {
      // 请求已发出但没有响应
      errorMessage = '网络错误，请检查您的连接'
    } else {
      // 其他错误
      errorMessage = error.message || '发生未知错误'
    }
    
    showToast({
      message: errorMessage,
      icon: 'fail'
    })
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-container {
  padding: 20px;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.header {
  text-align: center;
  margin-bottom: 40px;
  padding-top: 30px;
}

.header h1 {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.header p {
  font-size: 14px;
  color: #666;
}

.remember {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px 0;
  font-size: 14px;
}

.remember a {
  color: #1989fa;
  text-decoration: none;
}

.footer-links {
  text-align: center;
  margin-top: 20px;
  font-size: 14px;
}

.footer-links a {
  color: #666;
  text-decoration: none;
}

.register-link {
  color: #1989fa;
  font-weight: bold;
}
</style>