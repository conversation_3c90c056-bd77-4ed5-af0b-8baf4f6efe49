<template>
    <van-cell title="我的订单" />
    <van-grid>
        <van-grid-item 
        v-for="item, key in orderInfo"
        :icon="item.icon"  
        :text="item.text"
        :to="item.to" 
        :key="key" />
    </van-grid>
</template>
<script>
export default {
    data() {
        return {
            orderInfo: [
                { text: "全部订单", icon: "pending-payment",to:"/order/0" },
                { text: "待付款", icon: "balance-o",to:"/order/1" },
                { text: "待收货", icon: "paid",to:"/order/2" },
                { text: "待评价", icon: "flower-o",to:"/order/3" }
            ]
        }
    }
}
</script>
<style scoped></style>