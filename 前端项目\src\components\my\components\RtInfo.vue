<template>
    <div class="my-info">
        <img :src="defheader">
        <span>{{ userInfo.name }}</span>
        <p>{{ userInfo.desc }}</p>
    </div>
</template>
<script>
import defheader from "@/assets/images/my/default.jpg";
import { mapState } from 'pinia'
import { shopStore } from "@/store/shopStore"
export default {
    data() {
        return {
            defheader: defheader
        }
    },
    computed: {
        ...mapState(shopStore, ["userInfo"]),
    }
}
</script>
<style scoped>
.my-info {
    width: 100%;
    height: 187px;
    background: url("@/assets/images/my/bj.png") no-repeat;
    background-size: 100% 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.my-info img {
    width: 86px;
    height: 86px;
    border-radius: 50%;
    margin: 10px 0;
}

.my-info span {
    color: #ffffff;
    font-size: 15px;
}

.my-info p {
    font-size: 12px;
    color: #ffffff;
}</style>