<template>
    <van-swipe class="rt-swipe" :autoplay="3000" indicator-color="white">
        <van-swipe-item v-for="item, key in swiper" :key="key">
            <a :href="item.url"><van-image fit="fill" :src="item.src" /></a>
        </van-swipe-item>
        <template #indicator="{ active, total }">
            <div class="custom-indicator">{{ active + 1 }}/{{ total }}</div>
        </template>
    </van-swipe>
</template>
<script>
import swipe0 from "@/assets/images/swipe/swipe0.jpg"
import swipe1 from "@/assets/images/swipe/swipe1.jpg"
import swipe2 from "@/assets/images/swipe/swipe2.jpg"
import swipe3 from "@/assets/images/swipe/swipe3.jpg"
export default {
    data() {
        return {
            swiper: [
                { src: swipe0, url: "http://www.baidu.com" },
                { src: swipe1, url: "http://www.baidu.com" },
                { src: swipe2, url: "http://www.baidu.com" },
                { src: swipe3, url: "http://www.baidu.com" },
            ]
        }
    }
}
</script>
<style scoped>
.rt-swipe {
    height: 180px;
    margin-top: -5px;
}

.rt-swipe .van-swipe-item {
    color: #fff;
    font-size: 20px;
    text-align: center;
}

.rt-swipe .custom-indicator {
    position: absolute;
    right: 10px;
    bottom: 10px;
    color: #fff;
    padding: 2px 5px;
    font-size: 12px;
    background: rgba(0, 0, 0, 0.1);
}
</style>